<?php
/**
 * Test Video Progress API
 * This script tests if the video progress API is working correctly
 */

require_once 'includes/config.php';
require_once 'includes/database.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

echo "<h2>Video Progress API Test</h2>\n";
echo "<pre>\n";

try {
    // Test parameters
    $testUserId = 27;
    $testCourseId = 2;
    
    echo "Testing with User ID: $testUserId, Course ID: $testCourseId\n\n";

    // 1. Check if user exists
    echo "1. Checking if user exists...\n";
    $userStmt = $conn->prepare("SELECT id, name, username FROM users WHERE id = ?");
    $userStmt->bind_param("i", $testUserId);
    $userStmt->execute();
    $user = $userStmt->get_result()->fetch_assoc();
    
    if ($user) {
        echo "   ✅ User found: {$user['name']} ({$user['username']})\n";
    } else {
        echo "   ❌ User not found!\n";
        exit;
    }

    // 2. Check if course exists and get videos
    echo "\n2. Checking course and videos...\n";
    $courseStmt = $conn->prepare("SELECT id, title FROM courses WHERE id = ?");
    $courseStmt->bind_param("i", $testCourseId);
    $courseStmt->execute();
    $course = $courseStmt->get_result()->fetch_assoc();
    
    if ($course) {
        echo "   ✅ Course found: {$course['title']}\n";
    } else {
        echo "   ❌ Course not found!\n";
        exit;
    }

    // Get course videos
    $videosStmt = $conn->prepare("SELECT id, title, week_number FROM course_videos WHERE course_id = ? ORDER BY week_number, video_order LIMIT 5");
    $videosStmt->bind_param("i", $testCourseId);
    $videosStmt->execute();
    $videosResult = $videosStmt->get_result();
    $videos = [];
    while ($video = $videosResult->fetch_assoc()) {
        $videos[] = $video;
    }
    
    echo "   Found " . count($videos) . " videos in course\n";
    foreach ($videos as $video) {
        echo "     - Video {$video['id']}: {$video['title']} (Week {$video['week_number']})\n";
    }

    // 3. Check current progress data
    echo "\n3. Checking current progress data...\n";
    
    // Check user_video_progress table
    $progressStmt = $conn->prepare("
        SELECT video_id, 
               COALESCE(watch_duration_seconds, watch_time_seconds, 0) as watch_duration,
               COALESCE(is_completed, completed, 0) as is_completed,
               created_at, updated_at
        FROM user_video_progress 
        WHERE user_id = ? 
        ORDER BY updated_at DESC 
        LIMIT 10
    ");
    $progressStmt->bind_param("i", $testUserId);
    $progressStmt->execute();
    $progressResult = $progressStmt->get_result();
    
    echo "   Progress records in user_video_progress:\n";
    $progressCount = 0;
    while ($progress = $progressResult->fetch_assoc()) {
        $progressCount++;
        echo "     - Video {$progress['video_id']}: {$progress['watch_duration']}s watched, completed: {$progress['is_completed']}\n";
    }
    echo "   Total progress records: $progressCount\n";

    // Check user_activity_log table
    $activityStmt = $conn->prepare("
        SELECT related_id as video_id, details, created_at
        FROM user_activity_log 
        WHERE user_id = ? AND activity_type = 'video_progress'
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $activityStmt->bind_param("i", $testUserId);
    $activityStmt->execute();
    $activityResult = $activityStmt->get_result();
    
    echo "\n   Activity records in user_activity_log:\n";
    $activityCount = 0;
    while ($activity = $activityResult->fetch_assoc()) {
        $activityCount++;
        $details = json_decode($activity['details'], true);
        $watchDuration = $details['watch_duration'] ?? 'N/A';
        $isCompleted = $details['is_completed'] ?? false;
        echo "     - Video {$activity['video_id']}: {$watchDuration}s watched, completed: " . ($isCompleted ? 'Yes' : 'No') . "\n";
    }
    echo "   Total activity records: $activityCount\n";

    // 4. Test API endpoint directly
    echo "\n4. Testing API endpoint...\n";
    
    if (!empty($videos)) {
        $testVideo = $videos[0];
        echo "   Testing with video {$testVideo['id']}: {$testVideo['title']}\n";
        
        // Simulate API call data
        $testData = [
            'video_id' => $testVideo['id'],
            'watch_duration_seconds' => 120,
            'last_position_seconds' => 120,
            'completed' => 0
        ];
        
        echo "   Test data: " . json_encode($testData) . "\n";
        
        // Test the updateVideoProgress function from the API
        include_once '../api/video_progress.php';
        
        // We can't directly test the API endpoint here, but we can test the database function
        echo "   Note: To fully test the API, use a tool like Postman to POST to:\n";
        echo "   POST /api/video_progress.php\n";
        echo "   Headers: Authorization: Bearer <valid_jwt_token>\n";
        echo "   Body: " . json_encode($testData) . "\n";
    }

    // 5. Check table structures
    echo "\n5. Checking table structures...\n";
    
    // Check user_video_progress columns
    $result = $conn->query("SHOW COLUMNS FROM user_video_progress");
    echo "   user_video_progress columns:\n";
    while ($column = $result->fetch_assoc()) {
        echo "     - {$column['Field']} ({$column['Type']})\n";
    }
    
    // Check user_activity_log columns
    $result = $conn->query("SHOW COLUMNS FROM user_activity_log");
    echo "\n   user_activity_log columns:\n";
    while ($column = $result->fetch_assoc()) {
        echo "     - {$column['Field']} ({$column['Type']})\n";
    }

    // 6. Recommendations
    echo "\n6. Recommendations:\n";
    
    if ($progressCount == 0 && $activityCount == 0) {
        echo "   ❌ No video progress data found for this user\n";
        echo "   → Check if the Flutter app is successfully calling the video progress API\n";
        echo "   → Verify JWT token authentication is working\n";
        echo "   → Check network connectivity from the app\n";
    } elseif ($progressCount > 0 && $activityCount == 0) {
        echo "   ⚠️  Progress data exists but no activity logs\n";
        echo "   → The user_activity_log table may not be getting populated\n";
        echo "   → Check the logVideoActivity function in the API\n";
    } elseif ($progressCount == 0 && $activityCount > 0) {
        echo "   ⚠️  Activity logs exist but no progress data\n";
        echo "   → The user_video_progress table may not be getting updated\n";
        echo "   → Check the updateVideoProgress function in the API\n";
    } else {
        echo "   ✅ Both progress and activity data found\n";
        echo "   → Video analytics should be working correctly\n";
        echo "   → If analytics still show zero, check the analytics query logic\n";
    }

    echo "\n✅ Test completed!\n";

} catch (Exception $e) {
    echo "\n❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "</pre>\n";
?>

<style>
body { font-family: monospace; background: #f5f5f5; padding: 20px; }
pre { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
h2 { color: #333; }
</style>
