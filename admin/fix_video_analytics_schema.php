<?php
/**
 * Video Analytics Schema Fix
 * This script fixes database schema inconsistencies for video analytics
 * Run this once to ensure all tables have the correct structure
 */

require_once 'includes/config.php';
require_once 'includes/database.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

echo "<h2>Video Analytics Schema Fix</h2>\n";
echo "<pre>\n";

try {
    // Check current user_video_progress table structure
    echo "1. Checking user_video_progress table structure...\n";
    $result = $conn->query("SHOW COLUMNS FROM user_video_progress");
    $existingColumns = [];
    while ($row = $result->fetch_assoc()) {
        $existingColumns[] = $row['Field'];
    }
    echo "   Existing columns: " . implode(', ', $existingColumns) . "\n";

    // Add missing columns to user_video_progress table
    $columnsToAdd = [
        'watch_duration_seconds' => 'INT DEFAULT 0',
        'last_position_seconds' => 'INT DEFAULT 0', 
        'is_completed' => 'BOOLEAN DEFAULT FALSE',
        'is_unlocked' => 'BOOLEAN DEFAULT TRUE',
        'unlock_date' => 'DATE NULL',
        'completion_date' => 'DATETIME NULL'
    ];

    foreach ($columnsToAdd as $column => $definition) {
        if (!in_array($column, $existingColumns)) {
            echo "   Adding column: $column\n";
            $sql = "ALTER TABLE user_video_progress ADD COLUMN $column $definition";
            $conn->query($sql);
        } else {
            echo "   Column $column already exists\n";
        }
    }

    // Migrate data from old columns to new columns if needed
    echo "\n2. Migrating data to new column format...\n";
    
    // Map old column names to new ones
    $columnMappings = [
        'watch_time_seconds' => 'watch_duration_seconds',
        'completed' => 'is_completed'
    ];

    foreach ($columnMappings as $oldColumn => $newColumn) {
        if (in_array($oldColumn, $existingColumns) && in_array($newColumn, $existingColumns)) {
            echo "   Migrating data from $oldColumn to $newColumn...\n";
            $sql = "UPDATE user_video_progress SET $newColumn = $oldColumn WHERE $newColumn = 0 OR $newColumn IS NULL";
            $conn->query($sql);
        }
    }

    // Check and fix user_activity_log table
    echo "\n3. Checking user_activity_log table...\n";
    $result = $conn->query("SHOW TABLES LIKE 'user_activity_log'");
    if ($result->num_rows == 0) {
        echo "   Creating user_activity_log table...\n";
        $sql = "
        CREATE TABLE user_activity_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            activity_type VARCHAR(50) NOT NULL,
            related_id INT NULL,
            details JSON NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_activity_type (activity_type),
            INDEX idx_created_at (created_at),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )";
        $conn->query($sql);
    } else {
        echo "   user_activity_log table exists\n";
        
        // Check if details column is JSON type
        $result = $conn->query("SHOW COLUMNS FROM user_activity_log WHERE Field = 'details'");
        $detailsColumn = $result->fetch_assoc();
        if ($detailsColumn && strpos(strtolower($detailsColumn['Type']), 'json') === false) {
            echo "   Converting details column to JSON type...\n";
            $conn->query("ALTER TABLE user_activity_log MODIFY COLUMN details JSON NULL");
        }
    }

    // Check video_access_logs table
    echo "\n4. Checking video_access_logs table...\n";
    $result = $conn->query("SHOW TABLES LIKE 'video_access_logs'");
    if ($result->num_rows == 0) {
        echo "   Creating video_access_logs table...\n";
        $sql = "
        CREATE TABLE video_access_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            vimeo_id VARCHAR(50),
            video_id INT NOT NULL,
            user_id INT NOT NULL,
            action VARCHAR(50) NOT NULL,
            timestamp TIMESTAMP NULL,
            app_domain VARCHAR(255),
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_video_id (video_id),
            INDEX idx_user_id (user_id),
            INDEX idx_action (action),
            INDEX idx_timestamp (timestamp)
        )";
        $conn->query($sql);
    } else {
        echo "   video_access_logs table exists\n";
    }

    // Add indexes for better performance
    echo "\n5. Adding performance indexes...\n";
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_user_video_progress_user_completed ON user_video_progress(user_id, is_completed)",
        "CREATE INDEX IF NOT EXISTS idx_user_video_progress_video_completed ON user_video_progress(video_id, is_completed)",
        "CREATE INDEX IF NOT EXISTS idx_user_activity_log_user_type_related ON user_activity_log(user_id, activity_type, related_id)"
    ];

    foreach ($indexes as $indexSql) {
        try {
            $conn->query($indexSql);
            echo "   Added index successfully\n";
        } catch (Exception $e) {
            echo "   Index may already exist: " . $e->getMessage() . "\n";
        }
    }

    // Test data integrity
    echo "\n6. Testing data integrity...\n";
    
    // Count records in each table
    $tables = ['user_video_progress', 'user_activity_log', 'video_access_logs'];
    foreach ($tables as $table) {
        $result = $conn->query("SELECT COUNT(*) as count FROM $table");
        $count = $result->fetch_assoc()['count'];
        echo "   $table: $count records\n";
    }

    // Check for any video progress records without corresponding activity logs
    $result = $conn->query("
        SELECT COUNT(*) as orphaned_progress 
        FROM user_video_progress uvp 
        LEFT JOIN user_activity_log ual ON uvp.user_id = ual.user_id 
            AND uvp.video_id = ual.related_id 
            AND ual.activity_type = 'video_progress'
        WHERE ual.id IS NULL AND uvp.watch_duration_seconds > 0
    ");
    $orphanedCount = $result->fetch_assoc()['orphaned_progress'];
    echo "   Orphaned progress records (no activity log): $orphanedCount\n";

    if ($orphanedCount > 0) {
        echo "   Creating activity logs for orphaned progress records...\n";
        $sql = "
        INSERT INTO user_activity_log (user_id, activity_type, related_id, details, created_at)
        SELECT 
            uvp.user_id,
            'video_progress',
            uvp.video_id,
            JSON_OBJECT(
                'action', 'migrated_data',
                'watch_duration', uvp.watch_duration_seconds,
                'last_position', uvp.last_position_seconds,
                'is_completed', uvp.is_completed,
                'timestamp', COALESCE(uvp.updated_at, uvp.created_at)
            ),
            COALESCE(uvp.updated_at, uvp.created_at)
        FROM user_video_progress uvp 
        LEFT JOIN user_activity_log ual ON uvp.user_id = ual.user_id 
            AND uvp.video_id = ual.related_id 
            AND ual.activity_type = 'video_progress'
        WHERE ual.id IS NULL AND uvp.watch_duration_seconds > 0
        ";
        $conn->query($sql);
        echo "   Created activity logs for orphaned records\n";
    }

    echo "\n✅ Schema fix completed successfully!\n";
    echo "\nNext steps:\n";
    echo "1. Test the video analytics page with user ID 27 and course ID 2\n";
    echo "2. Check if Flutter app is sending video progress updates correctly\n";
    echo "3. Monitor the debug information on the analytics page\n";

} catch (Exception $e) {
    echo "\n❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "</pre>\n";
?>

<style>
body { font-family: monospace; background: #f5f5f5; padding: 20px; }
pre { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
h2 { color: #333; }
</style>
